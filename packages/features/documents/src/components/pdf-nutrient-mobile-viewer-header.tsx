'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import type { User } from '@supabase/supabase-js';
import { useCustomDownloadButton } from '../hooks/nutrient-viewer/toolbars/use-custom-download-button';
import { Instance } from '@nutrient-sdk/viewer';

interface PdfNutrientMobileViewerHeaderProps {
  instance: Instance | null;
  documentId: string | null | undefined;
  user: User;
  isUndoRedo: Boolean;
}

// List of interaction modes that should disable the button
const DISABLE_MODES = ['DOCUMENT_EDITOR', 'CONTENT_EDITOR', 'TEXT', 'SIGNATURE', 'STAMP_PICKER', 'STAMP_CUSTOM'];

export default function PdfNutrientMobileViewerHeader({
  instance,
  documentId,
  user,
  isUndoRedo,
}: PdfNutrientMobileViewerHeaderProps) {
  const customDownloadButton = useCustomDownloadButton(instance, documentId, user);
  const [canUndo, setCanUndo] = useState<Boolean>(false);
  const [canRedo, setCanRedo] = useState<Boolean>(false);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    setCanRedo(instance?.history.canRedo() ? true : false);
    setCanUndo(instance?.history.canUndo() ? true : false);
  }, [isUndoRedo, instance]);

  useEffect(() => {
    if (!instance) return;

    const handleViewStateChange = (viewState) => {
      const mode = viewState.interactionMode;
      setIsEditing(typeof mode === 'string' && DISABLE_MODES.includes(mode.toUpperCase()));
    };

    instance.addEventListener('viewState.change', handleViewStateChange);
    // Set initial state
    handleViewStateChange(instance.viewState);

    return () => {
      instance.removeEventListener('viewState.change', handleViewStateChange);
    };
  }, [instance]);

  return instance ? (
    <div className="px-4 py-2 border-b border-white bg-white flex justify-between items-center md:gap-3">
      <div className="flex flex-row gap-x-3">
        <button
          disabled={!canUndo}
          className="flex flex-col items-center gap-y-1 bg-white disabled:pointer-events-none disabled:opacity-50"
          onClick={instance?.history.undo}
        >
          <Image src="/images/pspdfkit/undo.svg" alt="PDFily Logo" width={20} height={20} className="w-[20px] h-auto" />
          <div className="text-xs text-black">Undo</div>
        </button>
        <button
          disabled={!canRedo}
          className="flex flex-col items-center gap-y-1 bg-white disabled:pointer-events-none disabled:opacity-50"
          onClick={instance?.history.redo}
        >
          <Image src="/images/pspdfkit/redo.svg" alt="PDFily Logo" width={20} height={20} className="w-[20px] h-auto" />
          <div className="text-xs text-black">Redo</div>
        </button>
      </div>
      {customDownloadButton && (
        <button
          onClick={customDownloadButton.onPress}
          disabled={isEditing}
          style={{
            color: '#F0401D',
            border: '1px solid #F0401D',
            borderRadius: '10px',
            padding: '10px 30px',
            background: 'white',
            fontWeight: 500,
            fontSize: '16px',
            lineHeight: '20px',
            opacity: isEditing ? 0.5 : 1,
            cursor: isEditing ? 'not-allowed' : 'pointer',
          }}
        >
          Done
        </button>
      )}
    </div>
  ) : (
    ''
  );
}
